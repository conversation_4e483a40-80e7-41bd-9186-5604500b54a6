#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL解析器测试脚本
用于测试CodeUp URL解析功能
"""

import re
from typing import Optional, Dict


class URLParser:
    """URL解析器"""
    
    @staticmethod
    def parse_codeup_url(url: str) -> Optional[Dict[str, str]]:
        """
        解析CodeUp URL
        例如: https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs
        """
        pattern = r'https://codeup\.aliyun\.com/([^/]+)/([^/]+)/([^/]+)/change/(\d+)'
        match = re.match(pattern, url)
        
        if match:
            organization_id = match.group(1)
            namespace = match.group(2)
            project_name = match.group(3)
            local_id = match.group(4)
            
            path_with_namespace = f"{organization_id}/{namespace}/{project_name}"
            
            return {
                'organization_id': organization_id,
                'namespace': namespace,
                'project_name': project_name,
                'path_with_namespace': path_with_namespace,
                'local_id': int(local_id)
            }
        
        return None


def test_url_parsing():
    """测试URL解析功能"""
    parser = URLParser()
    
    # 测试用例
    test_urls = [
        "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs",
        "https://codeup.aliyun.com/123456789/namespace/project/change/100/diffs",
        "https://codeup.aliyun.com/org/tech/my-project/change/999",
        "invalid-url",
        ""
    ]
    
    print("=== URL解析测试 ===\n")
    
    for i, url in enumerate(test_urls, 1):
        print(f"测试 {i}: {url}")
        result = parser.parse_codeup_url(url)
        
        if result:
            print("✓ 解析成功:")
            for key, value in result.items():
                print(f"  {key}: {value}")
        else:
            print("✗ 解析失败")
        
        print("-" * 50)


def interactive_test():
    """交互式测试"""
    parser = URLParser()
    
    print("\n=== 交互式URL解析测试 ===")
    print("输入 'quit' 退出测试")
    
    while True:
        url = input("\n请输入CodeUp URL: ").strip()
        
        if url.lower() == 'quit':
            break
        
        if not url:
            print("URL不能为空")
            continue
        
        result = parser.parse_codeup_url(url)
        
        if result:
            print("✓ 解析成功:")
            print(f"  组织ID: {result['organization_id']}")
            print(f"  命名空间: {result['namespace']}")
            print(f"  项目名: {result['project_name']}")
            print(f"  项目路径: {result['path_with_namespace']}")
            print(f"  合并请求ID: {result['local_id']}")
        else:
            print("✗ 解析失败，请检查URL格式")
            print("正确格式: https://codeup.aliyun.com/{org_id}/{namespace}/{project}/change/{mr_id}/diffs")


if __name__ == "__main__":
    # 运行预定义测试
    test_url_parsing()
    
    # 运行交互式测试
    interactive_test()
    
    print("\n测试完成！")
